#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多股票回测系统
支持同时回测多个股票的功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from 回测系统 import BacktestEngine, Strategy, Portfolio, Trade
from 回测分析 import BacktestAnalyzer

class MultiStockDataManager:
    """多股票数据管理器"""
    
    def __init__(self, pro_api=None, market_data_manager=None):
        self.pro_api = pro_api
        self.market_data_manager = market_data_manager  # 使用市场数据管理器
        self.stock_data = {}  # 存储各股票数据
        self.stock_list = []  # 股票列表
        
    def add_stock(self, ts_code: str, name: str = ""):
        """添加股票到列表"""
        if ts_code not in self.stock_list:
            self.stock_list.append(ts_code)
            
    def remove_stock(self, ts_code: str):
        """从列表中移除股票"""
        if ts_code in self.stock_list:
            self.stock_list.remove(ts_code)
        if ts_code in self.stock_data:
            del self.stock_data[ts_code]
            
    def load_stock_data(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """加载单个股票数据（支持API限流）"""
        try:
            # 优先使用市场数据管理器（支持限流和缓存）
            if self.market_data_manager:
                print(f"通过市场数据管理器获取 {ts_code} 数据...")
                data = self.market_data_manager.get_stock_daily_data(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date,
                    use_cache=True
                )
                if not data.empty:
                    self.stock_data[ts_code] = data
                    return data

            # 备用方案：直接使用API（不推荐，无限流保护）
            elif self.pro_api:
                print(f"⚠️ 直接API获取 {ts_code} 数据（无限流保护）...")
                data = self.pro_api.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
                data['trade_date'] = pd.to_datetime(data['trade_date'], format='%Y%m%d')
                data = data.sort_values('trade_date')
                self.stock_data[ts_code] = data
                return data
            else:
                raise ValueError("未设置数据接口")

        except Exception as e:
            print(f"加载股票 {ts_code} 数据失败: {str(e)}")
            return pd.DataFrame()
    
    def load_all_data(self, start_date: str, end_date: str, max_workers: int = 3,
                     batch_size: int = 200, progress_callback=None) -> Dict[str, pd.DataFrame]:
        """批量加载所有股票数据（支持API限流和分批处理）"""
        results = {}
        total_stocks = len(self.stock_list)

        # 检查是否需要分批处理
        if self.market_data_manager and total_stocks > batch_size:
            print(f"股票数量 {total_stocks} 超过批次大小 {batch_size}，启用分批处理模式...")
            return self._load_data_in_batches(start_date, end_date, batch_size, max_workers, progress_callback)

        # 如果使用市场数据管理器，优先使用批量获取（支持限流）
        if self.market_data_manager and len(self.stock_list) > 10:
            print(f"使用批量获取模式加载 {len(self.stock_list)} 只股票数据...")
            try:
                batch_results = self.market_data_manager.get_batch_stock_data(
                    self.stock_list, start_date, end_date, use_cache=True
                )

                for ts_code, data in batch_results.items():
                    if not data.empty:
                        self.stock_data[ts_code] = data
                        results[ts_code] = data
                    else:
                        print(f"警告: 股票 {ts_code} 数据为空")

                print(f"批量获取完成: 成功加载 {len(results)} 只股票数据")
                return results

            except Exception as e:
                print(f"批量获取失败，切换到逐个获取模式: {e}")

        # 逐个获取模式（降低并发数以配合API限流）
        def load_single_stock(ts_code):
            return ts_code, self.load_stock_data(ts_code, start_date, end_date)

        # 降低并发数，配合API限流（从5降到3）
        print(f"使用逐个获取模式加载 {len(self.stock_list)} 只股票数据...")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_stock = {executor.submit(load_single_stock, ts_code): ts_code
                             for ts_code in self.stock_list}

            completed_count = 0
            for future in as_completed(future_to_stock):
                ts_code, data = future.result()
                completed_count += 1

                # 计算数据加载进度 (0-40%)
                data_progress = (completed_count / len(self.stock_list)) * 40

                if not data.empty:
                    results[ts_code] = data
                    status_msg = f"{ts_code} 加载成功"
                    print(f"进度: {completed_count}/{len(self.stock_list)} - {status_msg}")
                else:
                    status_msg = f"{ts_code} 数据为空"
                    print(f"进度: {completed_count}/{len(self.stock_list)} - {status_msg}")

                # 回调进度更新
                if progress_callback:
                    progress_callback(
                        data_progress,
                        f"加载 {ts_code} ({completed_count}/{len(self.stock_list)})",
                        f"数据加载 ({completed_count/len(self.stock_list)*100:.1f}%)"
                    )

        print(f"数据加载完成: 成功加载 {len(results)} 只股票数据")
        return results

    def _load_data_in_batches(self, start_date: str, end_date: str,
                             batch_size: int, max_workers: int, progress_callback=None) -> Dict[str, pd.DataFrame]:
        """分批加载股票数据"""
        results = {}
        total_stocks = len(self.stock_list)

        # 将股票列表分成批次
        batches = [self.stock_list[i:i + batch_size]
                  for i in range(0, total_stocks, batch_size)]

        print(f"分批处理: 总共 {total_stocks} 只股票，分为 {len(batches)} 批，每批最多 {batch_size} 只")

        for batch_idx, batch_stocks in enumerate(batches, 1):
            print(f"\n开始处理第 {batch_idx}/{len(batches)} 批，包含 {len(batch_stocks)} 只股票...")

            # 检查API限流状态
            if self.market_data_manager:
                usage_stats = self.market_data_manager.get_api_usage_stats()
                rate_limiter = usage_stats['rate_limiter']
                remaining_calls = rate_limiter['remaining_calls']

                print(f"当前API状态: 剩余 {remaining_calls}/{rate_limiter['max_calls_per_minute']} 次调用")

                # 如果剩余调用次数不足，等待一段时间
                if remaining_calls < len(batch_stocks):
                    wait_time = 60  # 等待1分钟让API限制重置
                    print(f"API调用次数不足，等待 {wait_time} 秒...")
                    import time
                    time.sleep(wait_time)

            # 处理当前批次
            try:
                if self.market_data_manager:
                    # 使用批量获取
                    batch_results = self.market_data_manager.get_batch_stock_data(
                        batch_stocks, start_date, end_date, use_cache=True
                    )

                    for ts_code, data in batch_results.items():
                        if not data.empty:
                            self.stock_data[ts_code] = data
                            results[ts_code] = data
                        else:
                            print(f"警告: 股票 {ts_code} 数据为空")
                else:
                    # 逐个获取
                    for ts_code in batch_stocks:
                        data = self.load_stock_data(ts_code, start_date, end_date)
                        if not data.empty:
                            results[ts_code] = data

                print(f"第 {batch_idx} 批处理完成，成功加载 {len([s for s in batch_stocks if s in results])} 只股票")

            except Exception as e:
                print(f"第 {batch_idx} 批处理失败: {str(e)}")
                continue

        print(f"\n分批处理完成: 总共成功加载 {len(results)} 只股票数据")
        return results
    
    def get_stock_data(self, ts_code: str) -> pd.DataFrame:
        """获取指定股票数据"""
        return self.stock_data.get(ts_code, pd.DataFrame())
    
    def get_all_data(self) -> Dict[str, pd.DataFrame]:
        """获取所有股票数据"""
        return self.stock_data.copy()

class MultiStockBacktestEngine:
    """多股票回测引擎"""
    
    def __init__(self, initial_capital: float = 100000.0, commission_rate: float = 0.001,
                 allocation_method: str = "equal", market_data_manager=None):
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.allocation_method = allocation_method  # equal, market_cap, custom
        self.data_manager = MultiStockDataManager(market_data_manager=market_data_manager)
        self.strategy = None
        self.results = {}
        self.portfolio_results = {}
        
    def set_data_api(self, pro_api):
        """设置数据接口"""
        self.data_manager.pro_api = pro_api
        
    def add_stocks(self, stock_list: List[str]):
        """添加股票列表"""
        for ts_code in stock_list:
            self.data_manager.add_stock(ts_code)
            
    def set_strategy(self, strategy: Strategy):
        """设置交易策略"""
        self.strategy = strategy
        
    def set_allocation_weights(self, weights: Dict[str, float]):
        """设置自定义资金分配权重"""
        self.allocation_weights = weights
        
    def calculate_allocation(self, stock_list: List[str]) -> Dict[str, float]:
        """计算资金分配"""
        if self.allocation_method == "equal":
            # 等权重分配
            weight = 1.0 / len(stock_list)
            return {ts_code: weight for ts_code in stock_list}
        elif self.allocation_method == "custom" and hasattr(self, 'allocation_weights'):
            # 自定义权重
            return self.allocation_weights
        else:
            # 默认等权重
            weight = 1.0 / len(stock_list)
            return {ts_code: weight for ts_code in stock_list}
    
    def run_single_stock_backtest(self, ts_code: str, data: pd.DataFrame,
                                 allocated_capital: float) -> Dict:
        """运行单个股票的回测"""
        try:
            # 创建单股票回测引擎
            engine = BacktestEngine(initial_capital=allocated_capital,
                                  commission_rate=self.commission_rate)

            # 为每个股票创建独立的策略实例，避免状态污染
            strategy_copy = self._create_strategy_copy()
            engine.set_strategy(strategy_copy)
            engine.load_data(data)

            # 运行回测
            results = engine.run_backtest(ts_code)
            results['ts_code'] = ts_code
            results['allocated_capital'] = allocated_capital

            return results

        except Exception as e:
            print(f"股票 {ts_code} 回测失败: {str(e)}")
            return {
                'ts_code': ts_code,
                'allocated_capital': allocated_capital,
                'error': str(e),
                'initial_capital': allocated_capital,
                'final_value': allocated_capital,
                'total_return': 0.0,
                'total_trades': 0
            }

    def _create_strategy_copy(self):
        """为每个股票创建独立的策略副本"""
        from 回测系统 import MACDStrategy, KDJStrategy, CustomStrategy

        if isinstance(self.strategy, MACDStrategy):
            strategy_copy = MACDStrategy()
            strategy_copy.parameters = self.strategy.parameters.copy()
        elif isinstance(self.strategy, KDJStrategy):
            strategy_copy = KDJStrategy()
            strategy_copy.parameters = self.strategy.parameters.copy()
        elif isinstance(self.strategy, CustomStrategy):
            # 对于自定义策略，创建全新的实例
            strategy_copy = CustomStrategy(self.strategy.name, self.strategy.code)
            strategy_copy.parameters = self.strategy.parameters.copy()
            # 复制额外注入的全局变量，支持在多股票场景下也能直接使用同名变量（如 mh/MH）
            if hasattr(self.strategy, 'extra_globals') and isinstance(self.strategy.extra_globals, dict):
                try:
                    strategy_copy.extra_globals = self.strategy.extra_globals.copy()
                except Exception:
                    strategy_copy.extra_globals = dict(self.strategy.extra_globals)
        else:
            # 对于其他策略类型，尝试创建副本
            strategy_copy = type(self.strategy)(self.strategy.name)
            strategy_copy.parameters = self.strategy.parameters.copy()
            if hasattr(self.strategy, 'code'):
                strategy_copy.code = self.strategy.code

        return strategy_copy

    def run_multi_stock_backtest(self, start_date: str, end_date: str,
                               max_workers: int = 3, batch_size: int = 200,
                               progress_callback=None) -> Dict:
        """运行多股票回测"""
        if not self.strategy:
            raise ValueError("请先设置交易策略")
        
        if not self.data_manager.stock_list:
            raise ValueError("请先添加股票列表")
        
        total_stocks = len(self.data_manager.stock_list)

        # 阶段1: 数据加载 (0-40%)
        if progress_callback:
            progress_callback(0, f"开始加载 {total_stocks} 只股票的数据...", "数据加载")

        print(f"开始加载 {total_stocks} 只股票的数据...")

        # 加载所有股票数据（传入批次大小和进度回调）
        all_data = self.data_manager.load_all_data(start_date, end_date, max_workers, batch_size, progress_callback)

        if not all_data:
            raise ValueError("未能加载任何股票数据")

        loaded_count = len(all_data)
        if progress_callback:
            progress_callback(40, f"成功加载 {loaded_count} 只股票的数据", "数据加载完成")

        print(f"成功加载 {loaded_count} 只股票的数据")

        # 阶段2: 资金分配 (40-45%)
        if progress_callback:
            progress_callback(42, "计算资金分配...", "资金分配")

        # 计算资金分配
        allocations = self.calculate_allocation(list(all_data.keys()))

        if progress_callback:
            progress_callback(45, "资金分配完成", "准备回测")

        print("开始并行回测...")

        # 阶段3: 并行回测 (45-95%)
        stock_results = {}
        completed_count = 0

        def run_backtest_for_stock(ts_code, data):
            allocated_capital = self.initial_capital * allocations[ts_code]
            return self.run_single_stock_backtest(ts_code, data, allocated_capital)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_stock = {
                executor.submit(run_backtest_for_stock, ts_code, data): ts_code
                for ts_code, data in all_data.items()
            }

            for future in as_completed(future_to_stock):
                ts_code = future_to_stock[future]
                try:
                    result = future.result()
                    stock_results[ts_code] = result
                    completed_count += 1

                    # 计算回测进度 (45% + 50% * 完成比例)
                    backtest_progress = 45 + (completed_count / loaded_count) * 50
                    completion_rate = (completed_count / loaded_count) * 100

                    if progress_callback:
                        progress_callback(
                            backtest_progress,
                            f"完成股票 {ts_code} 回测 ({completed_count}/{loaded_count})",
                            f"回测进行中 ({completion_rate:.1f}%)"
                        )

                    print(f"完成股票 {ts_code} 回测 ({completed_count}/{loaded_count})")
                except Exception as e:
                    completed_count += 1
                    completion_rate = (completed_count / loaded_count) * 100
                    error_msg = f"股票 {ts_code} 回测异常: {str(e)}"

                    if progress_callback:
                        backtest_progress = 45 + (completed_count / loaded_count) * 50
                        progress_callback(
                            backtest_progress,
                            error_msg,
                            f"回测进行中 ({completion_rate:.1f}%)"
                        )

                    print(error_msg)

        # 阶段4: 计算组合结果 (95-100%)
        if progress_callback:
            progress_callback(95, "计算投资组合结果...", "结果计算")

        portfolio_results = self.calculate_portfolio_results(stock_results)
        
        self.results = stock_results
        self.portfolio_results = portfolio_results

        # 最终进度更新
        if progress_callback:
            progress_callback(100, "多股票回测完成", "分析完成")

        return {
            'stock_results': stock_results,
            'portfolio_results': portfolio_results,
            'allocations': allocations
        }
    
    def calculate_portfolio_results(self, stock_results: Dict) -> Dict:
        """计算投资组合整体结果"""
        total_initial = sum(result.get('initial_capital', 0) for result in stock_results.values())
        total_final = sum(result.get('final_value', 0) for result in stock_results.values())
        
        if total_initial == 0:
            return {}
        
        portfolio_return = (total_final - total_initial) / total_initial
        
        # 计算加权平均指标
        weights = {}
        total_trades = 0
        weighted_sharpe = 0
        weighted_max_dd = 0
        weighted_volatility = 0
        
        for ts_code, result in stock_results.items():
            if 'error' not in result:
                weight = result.get('initial_capital', 0) / total_initial
                weights[ts_code] = weight
                total_trades += result.get('total_trades', 0)
                
                weighted_sharpe += result.get('sharpe_ratio', 0) * weight
                weighted_max_dd += result.get('max_drawdown', 0) * weight
                weighted_volatility += result.get('volatility', 0) * weight
        
        # 计算组合收益率序列（用于计算更精确的风险指标）
        portfolio_equity_curve = self.calculate_portfolio_equity_curve(stock_results)
        
        portfolio_results = {
            'initial_capital': total_initial,
            'final_value': total_final,
            'total_return': portfolio_return,
            'total_trades': total_trades,
            'weighted_sharpe_ratio': weighted_sharpe,
            'weighted_max_drawdown': weighted_max_dd,
            'weighted_volatility': weighted_volatility,
            'stock_count': len([r for r in stock_results.values() if 'error' not in r]),
            'successful_stocks': len([r for r in stock_results.values() if 'error' not in r]),
            'failed_stocks': len([r for r in stock_results.values() if 'error' in r]),
            'equity_curve': portfolio_equity_curve
        }
        
        return portfolio_results
    
    def calculate_portfolio_equity_curve(self, stock_results: Dict) -> pd.DataFrame:
        """计算投资组合的资产净值曲线"""
        equity_curves = []
        
        for ts_code, result in stock_results.items():
            if 'error' not in result and 'equity_curve' in result:
                equity_df = result['equity_curve'].copy()
                equity_df['ts_code'] = ts_code
                equity_curves.append(equity_df)
        
        if not equity_curves:
            return pd.DataFrame()
        
        # 合并所有股票的净值曲线
        combined_df = pd.concat(equity_curves, ignore_index=True)
        
        # 按日期分组求和
        portfolio_equity = combined_df.groupby('date')['total_value'].sum().reset_index()
        portfolio_equity['daily_return'] = portfolio_equity['total_value'].pct_change()
        
        return portfolio_equity

class MultiStockAnalyzer:
    """多股票回测结果分析器"""

    def __init__(self, results: Dict):
        self.stock_results = results.get('stock_results', {})
        self.portfolio_results = results.get('portfolio_results', {})
        self.allocations = results.get('allocations', {})

    def generate_summary_report(self) -> str:
        """生成汇总报告"""
        report = []
        report.append("=" * 80)
        report.append("多股票回测结果汇总")
        report.append("=" * 80)

        # 组合整体表现
        if self.portfolio_results:
            report.append("\n【投资组合整体表现】")
            report.append("-" * 40)
            report.append(f"初始资金: ¥{self.portfolio_results.get('initial_capital', 0):,.2f}")
            report.append(f"最终资产: ¥{self.portfolio_results.get('final_value', 0):,.2f}")
            report.append(f"总收益率: {self.portfolio_results.get('total_return', 0):.2%}")
            report.append(f"加权夏普比率: {self.portfolio_results.get('weighted_sharpe_ratio', 0):.3f}")
            report.append(f"加权最大回撤: {self.portfolio_results.get('weighted_max_drawdown', 0):.2%}")
            report.append(f"总交易次数: {self.portfolio_results.get('total_trades', 0)}")
            report.append(f"成功回测股票: {self.portfolio_results.get('successful_stocks', 0)}")
            report.append(f"失败回测股票: {self.portfolio_results.get('failed_stocks', 0)}")

        # 个股表现排名
        report.append("\n【个股表现排名】")
        report.append("-" * 40)

        # 按收益率排序
        stock_performance = []
        for ts_code, result in self.stock_results.items():
            if 'error' not in result:
                stock_performance.append({
                    'ts_code': ts_code,
                    'return': result.get('total_return', 0),
                    'sharpe': result.get('sharpe_ratio', 0),
                    'max_dd': result.get('max_drawdown', 0),
                    'trades': result.get('total_trades', 0),
                    'allocation': self.allocations.get(ts_code, 0)
                })

        # 按收益率排序
        stock_performance.sort(key=lambda x: x['return'], reverse=True)

        report.append(f"{'排名':<4} {'股票代码':<12} {'收益率':<10} {'夏普比率':<10} {'最大回撤':<10} {'交易次数':<8} {'资金占比':<8}")
        report.append("-" * 70)

        # 显示全部股票，不再限制为前10名
        for i, stock in enumerate(stock_performance, 1):
            report.append(f"{i:<4} {stock['ts_code']:<12} {stock['return']:<10.2%} "
                         f"{stock['sharpe']:<10.3f} {stock['max_dd']:<10.2%} "
                         f"{stock['trades']:<8} {stock['allocation']:<8.1%}")

        # 失败股票
        failed_stocks = [ts_code for ts_code, result in self.stock_results.items()
                        if 'error' in result]
        if failed_stocks:
            report.append(f"\n【回测失败股票】")
            report.append("-" * 40)
            for ts_code in failed_stocks:
                error_msg = self.stock_results[ts_code].get('error', '未知错误')
                report.append(f"{ts_code}: {error_msg}")

        return "\n".join(report)

    def calculate_correlation_matrix(self) -> pd.DataFrame:
        """计算股票收益率相关性矩阵"""
        returns_data = {}

        for ts_code, result in self.stock_results.items():
            if 'error' not in result and 'equity_curve' in result:
                equity_df = result['equity_curve']
                if 'daily_return' in equity_df.columns:
                    returns_data[ts_code] = equity_df['daily_return'].dropna()

        if len(returns_data) < 2:
            return pd.DataFrame()

        # 对齐时间序列
        returns_df = pd.DataFrame(returns_data)
        correlation_matrix = returns_df.corr()

        return correlation_matrix

    def plot_performance_comparison(self):
        """绘制股票表现比较图"""
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 1. 收益率对比
        ax1 = axes[0, 0]
        stock_codes = []
        returns = []

        for ts_code, result in self.stock_results.items():
            if 'error' not in result:
                stock_codes.append(ts_code)
                returns.append(result.get('total_return', 0) * 100)

        if stock_codes:
            colors = ['green' if r >= 0 else 'red' for r in returns]
            bars = ax1.bar(range(len(stock_codes)), returns, color=colors)
            ax1.set_title('各股票收益率对比 (%)')
            ax1.set_ylabel('收益率 (%)')
            ax1.set_xticks(range(len(stock_codes)))
            ax1.set_xticklabels(stock_codes, rotation=45)
            ax1.grid(True, alpha=0.3)
            ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # 2. 夏普比率对比
        ax2 = axes[0, 1]
        sharpe_ratios = []

        for ts_code in stock_codes:
            result = self.stock_results[ts_code]
            sharpe_ratios.append(result.get('sharpe_ratio', 0))

        if sharpe_ratios:
            ax2.bar(range(len(stock_codes)), sharpe_ratios, color='blue', alpha=0.7)
            ax2.set_title('各股票夏普比率对比')
            ax2.set_ylabel('夏普比率')
            ax2.set_xticks(range(len(stock_codes)))
            ax2.set_xticklabels(stock_codes, rotation=45)
            ax2.grid(True, alpha=0.3)
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # 3. 最大回撤对比
        ax3 = axes[1, 0]
        max_drawdowns = []

        for ts_code in stock_codes:
            result = self.stock_results[ts_code]
            max_drawdowns.append(result.get('max_drawdown', 0) * 100)

        if max_drawdowns:
            ax3.bar(range(len(stock_codes)), max_drawdowns, color='red', alpha=0.7)
            ax3.set_title('各股票最大回撤对比 (%)')
            ax3.set_ylabel('最大回撤 (%)')
            ax3.set_xticks(range(len(stock_codes)))
            ax3.set_xticklabels(stock_codes, rotation=45)
            ax3.grid(True, alpha=0.3)

        # 4. 投资组合净值曲线
        ax4 = axes[1, 1]
        if 'equity_curve' in self.portfolio_results:
            portfolio_equity = self.portfolio_results['equity_curve']
            if not portfolio_equity.empty:
                ax4.plot(portfolio_equity.index, portfolio_equity['total_value'],
                        label='投资组合净值', linewidth=2, color='blue')
                ax4.set_title('投资组合净值曲线')
                ax4.set_ylabel('资产价值')
                ax4.set_xlabel('时间')
                ax4.legend()
                ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def plot_correlation_heatmap(self):
        """绘制相关性热力图"""
        correlation_matrix = self.calculate_correlation_matrix()

        if correlation_matrix.empty:
            print("数据不足，无法生成相关性矩阵")
            return

        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        fig, ax = plt.subplots(figsize=(10, 8))

        # 使用matplotlib绘制热力图
        im = ax.imshow(correlation_matrix.values, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)

        # 设置标签
        ax.set_xticks(range(len(correlation_matrix.columns)))
        ax.set_yticks(range(len(correlation_matrix.index)))
        ax.set_xticklabels(correlation_matrix.columns, rotation=45)
        ax.set_yticklabels(correlation_matrix.index)

        # 添加数值标注
        for i in range(len(correlation_matrix.index)):
            for j in range(len(correlation_matrix.columns)):
                text = ax.text(j, i, f'{correlation_matrix.iloc[i, j]:.2f}',
                             ha="center", va="center", color="black", fontsize=8)

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('相关系数')

        ax.set_title('股票收益率相关性矩阵')
        plt.tight_layout()
        plt.show()

    def export_detailed_results(self, filename: str = "多股票回测结果.xlsx"):
        """导出详细结果到Excel"""
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for ts_code, result in self.stock_results.items():
                    if 'error' not in result:
                        summary_data.append({
                            '股票代码': ts_code,
                            '初始资金': result.get('initial_capital', 0),
                            '最终资产': result.get('final_value', 0),
                            '总收益率': result.get('total_return', 0),
                            '年化收益率': result.get('annual_return', 0),
                            '波动率': result.get('volatility', 0),
                            '夏普比率': result.get('sharpe_ratio', 0),
                            '最大回撤': result.get('max_drawdown', 0),
                            '交易次数': result.get('total_trades', 0),
                            '胜率': result.get('win_rate', 0),
                            '资金占比': self.allocations.get(ts_code, 0)
                        })

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总结果', index=False)

                # 投资组合结果
                if self.portfolio_results:
                    portfolio_data = [{
                        '指标': '投资组合整体',
                        '初始资金': self.portfolio_results.get('initial_capital', 0),
                        '最终资产': self.portfolio_results.get('final_value', 0),
                        '总收益率': self.portfolio_results.get('total_return', 0),
                        '加权夏普比率': self.portfolio_results.get('weighted_sharpe_ratio', 0),
                        '加权最大回撤': self.portfolio_results.get('weighted_max_drawdown', 0),
                        '总交易次数': self.portfolio_results.get('total_trades', 0),
                        '成功股票数': self.portfolio_results.get('successful_stocks', 0),
                        '失败股票数': self.portfolio_results.get('failed_stocks', 0)
                    }]
                    portfolio_df = pd.DataFrame(portfolio_data)
                    portfolio_df.to_excel(writer, sheet_name='组合结果', index=False)

                # 相关性矩阵
                correlation_matrix = self.calculate_correlation_matrix()
                if not correlation_matrix.empty:
                    correlation_matrix.to_excel(writer, sheet_name='相关性矩阵')

            print(f"详细结果已导出到: {filename}")

        except Exception as e:
            print(f"导出结果失败: {str(e)}")
