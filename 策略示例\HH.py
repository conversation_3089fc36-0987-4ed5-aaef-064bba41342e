
# MACD金叉死叉策略
# DIF上穿DEA买入，DIF下穿DEA卖出

signals = [0] * len(data)

# 计算MACD指标
dif, dea, macd_hist = MACD(data['close'], MH, 26, 9)

for i in range(1, len(data)):
    if pd.notna(dif.iloc[i]) and pd.notna(dea.iloc[i]):
        # 金叉：DIF上穿DEA
        if dif.iloc[i] > dea.iloc[i] and dif.iloc[i-1] <= dea.iloc[i-1]:
            signals[i] = 1
        # 死叉：DIF下穿DEA
        elif dif.iloc[i] < dea.iloc[i] and dif.iloc[i-1] >= dea.iloc[i-1]:
            signals[i] = -1

